{"version": 3, "sources": ["../../@tiptap/extension-task-item/src/task-item.ts"], "sourcesContent": ["import {\n  KeyboardShortcutCommand, mergeAttributes, Node, wrappingInputRule,\n} from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nexport interface TaskItemOptions {\n  /**\n   * A callback function that is called when the checkbox is clicked while the editor is in readonly mode.\n   * @param node The prosemirror node of the task item\n   * @param checked The new checked state\n   * @returns boolean\n   */\n  onReadOnlyChecked?: (node: ProseMirrorNode, checked: boolean) => boolean\n\n  /**\n   * Controls whether the task items can be nested or not.\n   * @default false\n   * @example true\n   */\n  nested: boolean\n\n  /**\n   * HTML attributes to add to the task item element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * The node type for taskList nodes\n   * @default 'taskList'\n   * @example 'myCustomTaskList'\n   */\n  taskListTypeName: string\n}\n\n/**\n * Matches a task item to a - [ ] on input.\n */\nexport const inputRegex = /^\\s*(\\[([( |x])?\\])\\s$/\n\n/**\n * This extension allows you to create task items.\n * @see https://www.tiptap.dev/api/nodes/task-item\n */\nexport const TaskItem = Node.create<TaskItemOptions>({\n  name: 'taskItem',\n\n  addOptions() {\n    return {\n      nested: false,\n      HTMLAttributes: {},\n      taskListTypeName: 'taskList',\n    }\n  },\n\n  content() {\n    return this.options.nested ? 'paragraph block*' : 'paragraph+'\n  },\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      checked: {\n        default: false,\n        keepOnSplit: false,\n        parseHTML: element => {\n          const dataChecked = element.getAttribute('data-checked')\n\n          return dataChecked === '' || dataChecked === 'true'\n        },\n        renderHTML: attributes => ({\n          'data-checked': attributes.checked,\n        }),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: `li[data-type=\"${this.name}\"]`,\n        priority: 51,\n      },\n    ]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    return [\n      'li',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {\n        'data-type': this.name,\n      }),\n      [\n        'label',\n        [\n          'input',\n          {\n            type: 'checkbox',\n            checked: node.attrs.checked ? 'checked' : null,\n          },\n        ],\n        ['span'],\n      ],\n      ['div', 0],\n    ]\n  },\n\n  addKeyboardShortcuts() {\n    const shortcuts: {\n      [key: string]: KeyboardShortcutCommand\n    } = {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n\n    if (!this.options.nested) {\n      return shortcuts\n    }\n\n    return {\n      ...shortcuts,\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n    }\n  },\n\n  addNodeView() {\n    return ({\n      node, HTMLAttributes, getPos, editor,\n    }) => {\n      const listItem = document.createElement('li')\n      const checkboxWrapper = document.createElement('label')\n      const checkboxStyler = document.createElement('span')\n      const checkbox = document.createElement('input')\n      const content = document.createElement('div')\n\n      checkboxWrapper.contentEditable = 'false'\n      checkbox.type = 'checkbox'\n      checkbox.addEventListener('mousedown', event => event.preventDefault())\n      checkbox.addEventListener('change', event => {\n        // if the editor isn’t editable and we don't have a handler for\n        // readonly checks we have to undo the latest change\n        if (!editor.isEditable && !this.options.onReadOnlyChecked) {\n          checkbox.checked = !checkbox.checked\n\n          return\n        }\n\n        const { checked } = event.target as any\n\n        if (editor.isEditable && typeof getPos === 'function') {\n          editor\n            .chain()\n            .focus(undefined, { scrollIntoView: false })\n            .command(({ tr }) => {\n              const position = getPos()\n\n              if (typeof position !== 'number') {\n                return false\n              }\n              const currentNode = tr.doc.nodeAt(position)\n\n              tr.setNodeMarkup(position, undefined, {\n                ...currentNode?.attrs,\n                checked,\n              })\n\n              return true\n            })\n            .run()\n        }\n        if (!editor.isEditable && this.options.onReadOnlyChecked) {\n          // Reset state if onReadOnlyChecked returns false\n          if (!this.options.onReadOnlyChecked(node, checked)) {\n            checkbox.checked = !checkbox.checked\n          }\n        }\n      })\n\n      Object.entries(this.options.HTMLAttributes).forEach(([key, value]) => {\n        listItem.setAttribute(key, value)\n      })\n\n      listItem.dataset.checked = node.attrs.checked\n      checkbox.checked = node.attrs.checked\n\n      checkboxWrapper.append(checkbox, checkboxStyler)\n      listItem.append(checkboxWrapper, content)\n\n      Object.entries(HTMLAttributes).forEach(([key, value]) => {\n        listItem.setAttribute(key, value)\n      })\n\n      return {\n        dom: listItem,\n        contentDOM: content,\n        update: updatedNode => {\n          if (updatedNode.type !== this.type) {\n            return false\n          }\n\n          listItem.dataset.checked = updatedNode.attrs.checked\n          checkbox.checked = updatedNode.attrs.checked\n\n          return true\n        },\n      }\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          checked: match[match.length - 1] === 'x',\n        }),\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;AAuCO,IAAM,aAAa;AAMb,IAAA,WAAW,KAAK,OAAwB;EACnD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,QAAQ;MACR,gBAAgB,CAAA;MAChB,kBAAkB;;;EAItB,UAAO;AACL,WAAO,KAAK,QAAQ,SAAS,qBAAqB;;EAGpD,UAAU;EAEV,gBAAa;AACX,WAAO;MACL,SAAS;QACP,SAAS;QACT,aAAa;QACb,WAAW,aAAU;AACnB,gBAAM,cAAc,QAAQ,aAAa,cAAc;AAEvD,iBAAO,gBAAgB,MAAM,gBAAgB;;QAE/C,YAAY,iBAAe;UACzB,gBAAgB,WAAW;;MAE9B;;;EAIL,YAAS;AACP,WAAO;MACL;QACE,KAAK,iBAAiB,KAAK,IAAI;QAC/B,UAAU;MACX;;;EAIL,WAAW,EAAE,MAAM,eAAc,GAAE;AACjC,WAAO;MACL;MACA,gBAAgB,KAAK,QAAQ,gBAAgB,gBAAgB;QAC3D,aAAa,KAAK;OACnB;MACD;QACE;QACA;UACE;UACA;YACE,MAAM;YACN,SAAS,KAAK,MAAM,UAAU,YAAY;UAC3C;QACF;QACD,CAAC,MAAM;MACR;MACD,CAAC,OAAO,CAAC;;;EAIb,uBAAoB;AAClB,UAAM,YAEF;MACF,OAAO,MAAM,KAAK,OAAO,SAAS,cAAc,KAAK,IAAI;MACzD,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;;AAGhE,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACxB,aAAO;;AAGT,WAAO;MACL,GAAG;MACH,KAAK,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;;;EAI1D,cAAW;AACT,WAAO,CAAC,EACN,MAAM,gBAAgB,QAAQ,OAAM,MACjC;AACH,YAAM,WAAW,SAAS,cAAc,IAAI;AAC5C,YAAM,kBAAkB,SAAS,cAAc,OAAO;AACtD,YAAM,iBAAiB,SAAS,cAAc,MAAM;AACpD,YAAM,WAAW,SAAS,cAAc,OAAO;AAC/C,YAAM,UAAU,SAAS,cAAc,KAAK;AAE5C,sBAAgB,kBAAkB;AAClC,eAAS,OAAO;AAChB,eAAS,iBAAiB,aAAa,WAAS,MAAM,eAAc,CAAE;AACtE,eAAS,iBAAiB,UAAU,WAAQ;AAG1C,YAAI,CAAC,OAAO,cAAc,CAAC,KAAK,QAAQ,mBAAmB;AACzD,mBAAS,UAAU,CAAC,SAAS;AAE7B;;AAGF,cAAM,EAAE,QAAO,IAAK,MAAM;AAE1B,YAAI,OAAO,cAAc,OAAO,WAAW,YAAY;AACrD,iBACG,MAAK,EACL,MAAM,QAAW,EAAE,gBAAgB,MAAK,CAAE,EAC1C,QAAQ,CAAC,EAAE,GAAE,MAAM;AAClB,kBAAM,WAAW,OAAM;AAEvB,gBAAI,OAAO,aAAa,UAAU;AAChC,qBAAO;;AAET,kBAAM,cAAc,GAAG,IAAI,OAAO,QAAQ;AAE1C,eAAG,cAAc,UAAU,QAAW;cACpC,GAAG,gBAAA,QAAA,gBAAA,SAAA,SAAA,YAAa;cAChB;YACD,CAAA;AAED,mBAAO;UACT,CAAC,EACA,IAAG;;AAER,YAAI,CAAC,OAAO,cAAc,KAAK,QAAQ,mBAAmB;AAExD,cAAI,CAAC,KAAK,QAAQ,kBAAkB,MAAM,OAAO,GAAG;AAClD,qBAAS,UAAU,CAAC,SAAS;;;MAGnC,CAAC;AAED,aAAO,QAAQ,KAAK,QAAQ,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAK;AACnE,iBAAS,aAAa,KAAK,KAAK;MAClC,CAAC;AAED,eAAS,QAAQ,UAAU,KAAK,MAAM;AACtC,eAAS,UAAU,KAAK,MAAM;AAE9B,sBAAgB,OAAO,UAAU,cAAc;AAC/C,eAAS,OAAO,iBAAiB,OAAO;AAExC,aAAO,QAAQ,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAK;AACtD,iBAAS,aAAa,KAAK,KAAK;MAClC,CAAC;AAED,aAAO;QACL,KAAK;QACL,YAAY;QACZ,QAAQ,iBAAc;AACpB,cAAI,YAAY,SAAS,KAAK,MAAM;AAClC,mBAAO;;AAGT,mBAAS,QAAQ,UAAU,YAAY,MAAM;AAC7C,mBAAS,UAAU,YAAY,MAAM;AAErC,iBAAO;;;IAGb;;EAGF,gBAAa;AACX,WAAO;MACL,kBAAkB;QAChB,MAAM;QACN,MAAM,KAAK;QACX,eAAe,YAAU;UACvB,SAAS,MAAM,MAAM,SAAS,CAAC,MAAM;;OAExC;;;AAGN,CAAA;", "names": []}