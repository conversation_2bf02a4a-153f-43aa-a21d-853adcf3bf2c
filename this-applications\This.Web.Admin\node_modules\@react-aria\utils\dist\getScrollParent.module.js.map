{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAIM,SAAS,0CAAgB,IAAa,EAAE,gBAA0B;IACvE,IAAI,iBAAiC;IACrC,IAAI,CAAA,GAAA,yCAAW,EAAE,gBAAgB,mBAC/B,iBAAiB,eAAe,aAAa;IAG/C,MAAO,kBAAkB,CAAC,CAAA,GAAA,yCAAW,EAAE,gBAAgB,kBACrD,iBAAiB,eAAe,aAAa;IAG/C,OAAO,kBAAkB,SAAS,gBAAgB,IAAI,SAAS,eAAe;AAChF", "sources": ["packages/@react-aria/utils/src/getScrollParent.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollParent(node: Element, checkForOverflow?: boolean): Element {\n  let scrollableNode: Element | null = node;\n  if (isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  while (scrollableNode && !isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  return scrollableNode || document.scrollingElement || document.documentElement;\n}\n\n"], "names": [], "version": 3, "file": "getScrollParent.module.js.map"}