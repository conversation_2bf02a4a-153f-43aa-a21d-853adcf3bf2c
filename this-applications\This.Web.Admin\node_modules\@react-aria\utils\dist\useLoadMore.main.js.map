{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AAwBM,SAAS,0CAAY,KAAoB,EAAE,GAAkC;IAClF,IAAI,aAAC,SAAS,cAAE,UAAU,gBAAE,eAAe,UAAG,KAAK,EAAC,GAAG;IAEvD,iEAAiE;IACjE,IAAI,eAAe,CAAA,GAAA,mBAAK,EAAE;IAC1B,IAAI,YAAY,CAAA,GAAA,mBAAK,EAAE;IACvB,IAAI,WAAW,CAAA,GAAA,wBAAU,EAAE;QACzB,IAAI,IAAI,OAAO,IAAI,CAAC,aAAa,OAAO,IAAI,YAAY;YACtD,IAAI,iBAAiB,IAAI,OAAO,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,OAAO,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,YAAY,GAAG;YAE9H,IAAI,gBAAgB;gBAClB,aAAa,OAAO,GAAG;gBACvB;YACF;QACF;IACF,GAAG;QAAC;QAAY;QAAK;KAAa;IAElC,IAAI,YAAY,CAAA,GAAA,mBAAK,EAAE;IACvB,CAAA,GAAA,yCAAc,EAAE;QACd,6DAA6D;QAC7D,wCAAwC;QACxC,IAAI,UAAU,UAAU,OAAO,EAAE;YAC/B,aAAa,OAAO,GAAG;YACvB,UAAU,OAAO,GAAG;QACtB;QAEA,0HAA0H;QAC1H,4HAA4H;QAC5H,gFAAgF;QAChF,IAAI,iBAAiB,CAAA,gBAAA,0BAAA,IAAK,OAAO,KAC5B,CAAC,aAAa,OAAO,IACrB,cACC,CAAA,CAAC,SAAS,UAAU,UAAU,OAAO,AAAD,KACrC,IAAI,OAAO,CAAC,YAAY,KAAK,IAAI,OAAO,CAAC,YAAY;QAE1D,IAAI,gBAAgB;YAClB,aAAa,OAAO,GAAG;YACvB,uBAAA,iCAAA;QACF;QAEA,UAAU,OAAO,GAAG;IACtB,GAAG;QAAC;QAAW;QAAY;QAAO;QAAK;KAAM;IAE7C,0DAA0D;IAC1D,oEAAoE;IACpE,kHAAkH;IAClH,CAAA,GAAA,kCAAO,EAAE,KAAK,UAAU;AAC1B", "sources": ["packages/@react-aria/utils/src/useLoadMore.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject, useCallback, useRef} from 'react';\nimport {useEvent} from './useEvent';\n\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport interface LoadMoreProps {\n  /** Whether data is currently being loaded. */\n  isLoading?: boolean,\n  /** Handler that is called when more items should be loaded, e.g. while scrolling near the bottom.  */\n  onLoadMore?: () => void,\n  /**\n   * The amount of offset from the bottom of your scrollable region that should trigger load more.\n   * Uses a percentage value relative to the scroll body's client height. Load more is then triggered\n   * when your current scroll position's distance from the bottom of the currently loaded list of items is less than\n   * or equal to the provided value. (e.g. 1 = 100% of the scroll region's height).\n   * @default 1\n   */\n  scrollOffset?: number,\n  /** The data currently loaded. */\n  items?: any\n}\n\nexport function useLoadMore(props: LoadMoreProps, ref: RefObject<HTMLElement | null>): void {\n  let {isLoading, onLoadMore, scrollOffset = 1, items} = props;\n\n  // Handle scrolling, and call onLoadMore when nearing the bottom.\n  let isLoadingRef = useRef(isLoading);\n  let prevProps = useRef(props);\n  let onScroll = useCallback(() => {\n    if (ref.current && !isLoadingRef.current && onLoadMore) {\n      let shouldLoadMore = ref.current.scrollHeight - ref.current.scrollTop - ref.current.clientHeight < ref.current.clientHeight * scrollOffset;\n\n      if (shouldLoadMore) {\n        isLoadingRef.current = true;\n        onLoadMore();\n      }\n    }\n  }, [onLoadMore, ref, scrollOffset]);\n\n  let lastItems = useRef(items);\n  useLayoutEffect(() => {\n    // Only update isLoadingRef if props object actually changed,\n    // not if a local state change occurred.\n    if (props !== prevProps.current) {\n      isLoadingRef.current = isLoading;\n      prevProps.current = props;\n    }\n\n    // TODO: Eventually this hook will move back into RAC during which we will accept the collection as a option to this hook.\n    // We will only load more if the collection has changed after the last load to prevent multiple onLoadMore from being called\n    // while the data from the last onLoadMore is being processed by RAC collection.\n    let shouldLoadMore = ref?.current\n      && !isLoadingRef.current\n      && onLoadMore\n      && (!items || items !== lastItems.current)\n      && ref.current.clientHeight === ref.current.scrollHeight;\n\n    if (shouldLoadMore) {\n      isLoadingRef.current = true;\n      onLoadMore?.();\n    }\n\n    lastItems.current = items;\n  }, [isLoading, onLoadMore, props, ref, items]);\n\n  // TODO: maybe this should still just return scroll props?\n  // Test against case where the ref isn't defined when this is called\n  // Think this was a problem when trying to attach to the scrollable body of the table in OnLoadMoreTableBodyScroll\n  useEvent(ref, 'scroll', onScroll);\n}\n"], "names": [], "version": 3, "file": "useLoadMore.main.js.map"}