{"mappings": ";;AAAA;;;;;;;;;;CAUC,GAED,uCAAuC;AAIhC,SAAS,0CAAe,KAAQ,EAAE,OAAgC;IACvE,uGAAuG;IACvG,yGAAyG;IACzG,IAAI,YAAY,CAAA,GAAA,aAAK,EAAY;IACjC,IAAI,SAAS,UAAU,OAAO,IAAI,QAAQ,OAAO,UAAU,OAAO,GAChE,QAAQ,UAAU,OAAO;IAG3B,UAAU,OAAO,GAAG;IACpB,OAAO;AACT", "sources": ["packages/@react-aria/utils/src/useDeepMemo.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/* eslint-disable rulesdir/pure-render */\n\nimport {useRef} from 'react';\n\nexport function useDeepMemo<T>(value: T, isEqual: (a: T, b: T) => boolean): T {\n  // Using a ref during render is ok here because it's only an optimization – both values are equivalent.\n  // If a render is thrown away, it'll still work the same no matter if the next render is the same or not.\n  let lastValue = useRef<T | null>(null);\n  if (value && lastValue.current && isEqual(value, lastValue.current)) {\n    value = lastValue.current;\n  }\n\n  lastValue.current = value;\n  return value;\n}\n"], "names": [], "version": 3, "file": "useDeepMemo.module.js.map"}