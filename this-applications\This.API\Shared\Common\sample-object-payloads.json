{"organizationPayloads": [{"metadataProperties": {"ObjectId": "033f0a76-8f1d-4537-8045-54b08e621014", "RefId": "a1111111-1111-1111-1111-************", "ParentObjectValueId": null, "OrganizationName": "Skyline Properties Ltd", "OrganizationEmail": "<EMAIL>", "OrganizationPhone": "+971-4-123-4567", "OrganizationWebsite": "https://skylineproperties.com", "OrganizationLogo": "https://skylineproperties.com/logo.png", "OrganizationDescription": "Premium real estate developer in Dubai", "OrganizationAddress": "<PERSON> Road, Dubai, UAE", "OrganizationLatitude": "25.2048", "OrganizationLongitude": "55.2708"}}, {"metadataProperties": {"ObjectId": "033f0a76-8f1d-4537-8045-54b08e621014", "RefId": "a2222222-2222-2222-2222-************", "ParentObjectValueId": null, "OrganizationName": "Emirates Real Estate Group", "OrganizationEmail": "<EMAIL>", "OrganizationPhone": "+971-2-987-6543", "OrganizationWebsite": "https://emiratesrealestate.ae", "OrganizationLogo": "https://emiratesrealestate.ae/logo.png", "OrganizationDescription": "Leading property development company in Abu Dhabi", "OrganizationAddress": "Corniche Road, Abu Dhabi, UAE", "OrganizationLatitude": "24.4539", "OrganizationLongitude": "54.3773"}}, {"metadataProperties": {"ObjectId": "033f0a76-8f1d-4537-8045-54b08e621014", "RefId": "a3333333-3333-3333-3333-333333333333", "ParentObjectValueId": null, "OrganizationName": "DAMAC Properties", "OrganizationEmail": "<EMAIL>", "OrganizationPhone": "+971-4-423-1100", "OrganizationWebsite": "https://damacproperties.com", "OrganizationLogo": "https://damacproperties.com/logo.png", "OrganizationDescription": "Luxury real estate developer in the Middle East", "OrganizationAddress": "Business Bay, Dubai, UAE", "OrganizationLatitude": "25.1872", "OrganizationLongitude": "55.2632"}}, {"metadataProperties": {"ObjectId": "033f0a76-8f1d-4537-8045-54b08e621014", "RefId": "a4444444-4444-4444-4444-444444444444", "ParentObjectValueId": null, "OrganizationName": "Emaar Properties", "OrganizationEmail": "<EMAIL>", "OrganizationPhone": "+971-4-367-3333", "OrganizationWebsite": "https://emaar.com", "OrganizationLogo": "https://emaar.com/logo.png", "OrganizationDescription": "Global property developer and provider of premium lifestyles", "OrganizationAddress": "Downtown Dubai, UAE", "OrganizationLatitude": "25.1972", "OrganizationLongitude": "55.2744"}}, {"metadataProperties": {"ObjectId": "033f0a76-8f1d-4537-8045-54b08e621014", "RefId": "a5555555-5555-5555-5555-555555555555", "ParentObjectValueId": null, "OrganizationName": "Nakheel Properties", "OrganizationEmail": "<EMAIL>", "OrganizationPhone": "+971-4-390-3333", "OrganizationWebsite": "https://nakheel.com", "OrganizationLogo": "https://nakheel.com/logo.png", "OrganizationDescription": "Dubai's master developer creating innovative communities", "OrganizationAddress": "Palm Jumeirah, Dubai, UAE", "OrganizationLatitude": "25.1124", "OrganizationLongitude": "55.1390"}}], "buildingPayloads": [{"metadataProperties": {"ObjectId": "8f4e1201-1d80-4f4b-8dc8-a190be0831bb", "RefId": "b1111111-1111-1111-1111-************", "ParentObjectValueId": "a1111111-1111-1111-1111-************", "BuildingName": "Azure Tower", "NumberOfFloors": "45", "ElevatorType": "High-speed passenger elevators", "Facade": "Glass and steel modern facade", "ParkingType": "Underground automated parking", "DeliveryDate": "2024-12-31"}}, {"metadataProperties": {"ObjectId": "8f4e1201-1d80-4f4b-8dc8-a190be0831bb", "RefId": "b2222222-2222-2222-2222-************", "ParentObjectValueId": "a2222222-2222-2222-2222-************", "BuildingName": "Golden Heights", "NumberOfFloors": "38", "ElevatorType": "Premium residential elevators", "Facade": "Contemporary stone and glass facade", "ParkingType": "Multi-level covered parking", "DeliveryDate": "2025-06-30"}}, {"metadataProperties": {"ObjectId": "8f4e1201-1d80-4f4b-8dc8-a190be0831bb", "RefId": "b3333333-3333-3333-3333-333333333333", "ParentObjectValueId": "a3333333-3333-3333-3333-333333333333", "BuildingName": "<PERSON>", "NumberOfFloors": "52", "ElevatorType": "High-speed smart elevators", "Facade": "Curved glass and aluminum facade", "ParkingType": "Robotic parking system", "DeliveryDate": "2025-03-15"}}, {"metadataProperties": {"ObjectId": "8f4e1201-1d80-4f4b-8dc8-a190be0831bb", "RefId": "b4444444-4444-4444-4444-444444444444", "ParentObjectValueId": "a4444444-4444-4444-4444-444444444444", "BuildingName": "<PERSON><PERSON><PERSON>", "NumberOfFloors": "63", "ElevatorType": "Destination control elevators", "Facade": "Premium glass curtain wall", "ParkingType": "Valet parking service", "DeliveryDate": "2024-09-30"}}, {"metadataProperties": {"ObjectId": "8f4e1201-1d80-4f4b-8dc8-a190be0831bb", "RefId": "b5555555-5555-5555-5555-555555555555", "ParentObjectValueId": "a5555555-5555-5555-5555-555555555555", "BuildingName": "Palm Residences", "NumberOfFloors": "28", "ElevatorType": "Panoramic glass elevators", "Facade": "Tropical modern architecture", "ParkingType": "Beach-side parking", "DeliveryDate": "2025-01-20"}}], "floorPayloads": [{"metadataProperties": {"ObjectId": "1bee1216-4bf1-4527-88b1-ee644c788814", "RefId": "c1111111-1111-1111-1111-************", "ParentObjectValueId": "b1111111-1111-1111-1111-************", "FloorNumber": "15", "NumberOfUnits": "8", "FloorPlan": "https://azure-tower.com/floor-plans/floor-15.pdf"}}, {"metadataProperties": {"ObjectId": "1bee1216-4bf1-4527-88b1-ee644c788814", "RefId": "c2222222-2222-2222-2222-************", "ParentObjectValueId": "b1111111-1111-1111-1111-************", "FloorNumber": "25", "NumberOfUnits": "6", "FloorPlan": "https://azure-tower.com/floor-plans/floor-25.pdf"}}, {"metadataProperties": {"ObjectId": "1bee1216-4bf1-4527-88b1-ee644c788814", "RefId": "c3333333-3333-3333-3333-333333333333", "ParentObjectValueId": "b2222222-2222-2222-2222-************", "FloorNumber": "20", "NumberOfUnits": "4", "FloorPlan": "https://golden-heights.com/floor-plans/floor-20.pdf"}}, {"metadataProperties": {"ObjectId": "1bee1216-4bf1-4527-88b1-ee644c788814", "RefId": "c4444444-4444-4444-4444-444444444444", "ParentObjectValueId": "b3333333-3333-3333-3333-333333333333", "FloorNumber": "35", "NumberOfUnits": "6", "FloorPlan": "https://marina-pinnacle.com/floor-plans/floor-35.pdf"}}, {"metadataProperties": {"ObjectId": "1bee1216-4bf1-4527-88b1-ee644c788814", "RefId": "c5555555-5555-5555-5555-555555555555", "ParentObjectValueId": "b4444444-4444-4444-4444-444444444444", "FloorNumber": "50", "NumberOfUnits": "2", "FloorPlan": "https://burj-vista.com/floor-plans/floor-50.pdf"}}], "unitPayloads": [{"metadataProperties": {"ObjectId": "60f06a2d-ab5d-4d37-8840-1216ed65cd28", "RefId": "d1111111-1111-1111-1111-************", "ParentObjectValueId": "c1111111-1111-1111-1111-************", "Type": "2BR Apartment", "BedroomCount": "2", "ToiletCount": "3", "AreaTotal": "1250", "AreaInternal": "1100", "PriceSelling": "2500000", "Price100Percent": "2500000", "Status": "Available", "View": "Dubai Marina and sea view", "FinishingType": "Premium finishing", "PaymentPlan": "1750000", "FloorPlan": "https://azure-tower.com/unit-plans/1501.pdf", "Images": "https://azure-tower.com/gallery/unit-1501/", "SoldOut": "false"}}, {"metadataProperties": {"ObjectId": "60f06a2d-ab5d-4d37-8840-1216ed65cd28", "RefId": "d2222222-2222-2222-2222-************", "ParentObjectValueId": "c2222222-2222-2222-2222-************", "Type": "3BR Penthouse", "BedroomCount": "3", "ToiletCount": "4", "AreaTotal": "1850", "AreaInternal": "1650", "PriceSelling": "3750000", "Price100Percent": "3750000", "Status": "Available", "View": "City skyline and Burj Khalifa view", "FinishingType": "Lu<PERSON><PERSON> finishing", "PaymentPlan": "2250000", "FloorPlan": "https://azure-tower.com/unit-plans/2502.pdf", "Images": "https://azure-tower.com/gallery/unit-2502/", "SoldOut": "false"}}, {"metadataProperties": {"ObjectId": "60f06a2d-ab5d-4d37-8840-1216ed65cd28", "RefId": "d3333333-3333-3333-3333-333333333333", "ParentObjectValueId": "c3333333-3333-3333-3333-333333333333", "Type": "1BR Apartment", "BedroomCount": "1", "ToiletCount": "2", "AreaTotal": "850", "AreaInternal": "750", "PriceSelling": "1800000", "Price100Percent": "1800000", "Status": "Available", "View": "Garden and pool view", "FinishingType": "Standard finishing", "PaymentPlan": "1440000", "FloorPlan": "https://golden-heights.com/unit-plans/2001.pdf", "Images": "https://golden-heights.com/gallery/unit-2001/", "SoldOut": "false"}}, {"metadataProperties": {"ObjectId": "60f06a2d-ab5d-4d37-8840-1216ed65cd28", "RefId": "d4444444-4444-4444-4444-444444444444", "ParentObjectValueId": "c4444444-4444-4444-4444-444444444444", "Type": "4BR Penthouse", "BedroomCount": "4", "ToiletCount": "5", "AreaTotal": "2200", "AreaInternal": "2000", "PriceSelling": "5500000", "Price100Percent": "5500000", "Status": "Reserved", "View": "Marina and sea panoramic view", "FinishingType": "Ultra-luxury finishing", "PaymentPlan": "2750000", "FloorPlan": "https://marina-pinnacle.com/unit-plans/3501.pdf", "Images": "https://marina-pinnacle.com/gallery/unit-3501/", "SoldOut": "false"}}, {"metadataProperties": {"ObjectId": "60f06a2d-ab5d-4d37-8840-1216ed65cd28", "RefId": "d5555555-5555-5555-5555-555555555555", "ParentObjectValueId": "c5555555-5555-5555-5555-555555555555", "Type": "5BR Sky Villa", "BedroomCount": "5", "ToiletCount": "6", "AreaTotal": "3500", "AreaInternal": "3200", "PriceSelling": "12000000", "Price100Percent": "12000000", "Status": "Available", "View": "Burj Khalifa and Downtown view", "FinishingType": "Bespoke luxury finishing", "PaymentPlan": "4800000", "FloorPlan": "https://burj-vista.com/unit-plans/5001.pdf", "Images": "https://burj-vista.com/gallery/unit-5001/", "SoldOut": "false"}}], "attachmentPayloads": [{"metadataProperties": {"ObjectId": "a7487160-33be-410d-87c6-bedf49211cc5", "RefId": "e1111111-1111-1111-1111-************", "ParentObjectValueId": "d1111111-1111-1111-1111-************", "FileName": "floor_plan_unit_1501.pdf", "FileURL": "https://azure-tower.com/attachments/floor_plan_unit_1501.pdf", "FileType": "PDF", "FileSize": "2048", "Status": "Active"}}, {"metadataProperties": {"ObjectId": "a7487160-33be-410d-87c6-bedf49211cc5", "RefId": "e2222222-2222-2222-2222-************", "ParentObjectValueId": "d2222222-2222-2222-2222-************", "FileName": "unit_images_2502.zip", "FileURL": "https://azure-tower.com/attachments/unit_images_2502.zip", "FileType": "ZIP", "FileSize": "15360", "Status": "Active"}}], "enquiryPayloads": [{"metadataProperties": {"ObjectId": "f03cbcdd-6b6d-40b3-8dac-ad079389dae8", "RefId": "f1111111-1111-1111-1111-************", "ParentObjectValueId": "d1111111-1111-1111-1111-************", "InterestedUnit": "85", "Customer": "<PERSON>", "Source": "Website", "EnquiryID": "ENQ-2024-001", "Notes": "Customer interested in 2BR apartment with marina view. Prefers higher floor.", "Status": "Active"}}, {"metadataProperties": {"ObjectId": "f03cbcdd-6b6d-40b3-8dac-ad079389dae8", "RefId": "f2222222-2222-2222-2222-************", "ParentObjectValueId": "d2222222-2222-2222-2222-************", "InterestedUnit": "95", "Customer": "<PERSON>", "Source": "Referral", "EnquiryID": "ENQ-2024-002", "Notes": "International buyer looking for luxury penthouse with city skyline view.", "Status": "Follow-up"}}], "bookingPayloads": [{"metadataProperties": {"ObjectId": "3defab0f-5690-4c91-baa4-f2247aab4f08", "RefId": "b1111111-1111-1111-1111-************", "ParentObjectValueId": "f1111111-1111-1111-1111-************", "BookingDate": "2024-06-15", "Status": "Confirmed", "SpecialTermsNotes": "Early bird discount applied - 5% off total price", "PaymentPlan": "70-30 Payment Plan", "AgreementDocument": "https://azure-tower.com/agreements/booking_001.pdf", "UnitApartment": "Unit 1501 - 2BR Marina View", "BookingID": "BK-2024-001", "Customer": "<PERSON>"}}], "paymentPlanPayloads": [{"metadataProperties": {"ObjectId": "08715fe7-40ec-45e0-bc0a-1bba0c47e9d1", "RefId": "p1111111-1111-1111-1111-************", "ParentObjectValueId": "b1111111-1111-1111-1111-************", "PaymentPlanName": "70-30 Standard Plan", "TotalPrice": "2500000", "Milestones": "Booking: 30%, Handover: 70%", "LinkedUnitBooking": "BK-2024-001", "Status": "Active"}}], "paymentPayloads": [{"metadataProperties": {"ObjectId": "bb289b15-bd8d-4820-bb9d-4d94b532590c", "RefId": "y1111111-1111-1111-1111-************", "ParentObjectValueId": "08715fe7-40ec-45e0-bc0a-1bba0c47e9d1", "PaymentStatus": "Completed", "PaymentAmount": "750000", "PaymentMilestone": "Booking Payment", "PaymentPlanReference": "70-30 Standard Plan", "BookingReference": "BK-2024-001", "CustomerReference": "<PERSON>", "PaymentID": "PAY-2024-001", "PaymentDate": "2024-06-15", "ReceiptNumber": "RCP-001-2024", "PaymentMethod": "Bank Transfer", "Notes": "Initial booking payment received successfully", "Attachment": "https://azure-tower.com/receipts/PAY-2024-001.pdf", "TransactionReference": "TXN-AED-750000-********"}}], "channelPartnerPayloads": [{"metadataProperties": {"ObjectId": "********-60ca-45e1-8915-a545ad50819b", "RefId": "c1111111-1111-1111-1111-************", "ParentObjectValueId": "a1111111-1111-1111-1111-************", "ContactPerson": "<PERSON>", "Address": "Business Bay, Dubai, UAE", "Organization": "Hassan Real Estate Brokers", "PhoneNumber": "+971-50-123-4567", "CommissionStructure": "3.5", "Email": "<EMAIL>", "ChannelPartnerName": "Hassan Real Estate Brokers"}}, {"metadataProperties": {"ObjectId": "********-60ca-45e1-8915-a545ad50819b", "RefId": "c2222222-2222-2222-2222-************", "ParentObjectValueId": "a2222222-2222-2222-2222-************", "ContactPerson": "Fatima Al Zahra", "Address": "Marina Walk, Dubai Marina, UAE", "Organization": "Marina Properties Consultants", "PhoneNumber": "+971-55-987-6543", "CommissionStructure": "4.0", "Email": "<EMAIL>", "ChannelPartnerName": "Marina Properties Consultants"}}], "channelPartnerCustomerPayloads": [{"metadataProperties": {"ObjectId": "eb2cbe96-6057-4786-9d94-3de9862b785a", "RefId": "h1111111-1111-1111-1111-************", "ParentObjectValueId": "c1111111-1111-1111-1111-************", "CustomerName": "<PERSON><PERSON>", "Address": "Jumeirah Lake Towers, Dubai, UAE", "PhoneNumber": "+971-52-345-6789", "KYCDetails": "Emirates ID: 784-1985-1234567-8, Passport: H1234567", "Email": "<EMAIL>", "ChannelPartner": "Hassan Real Estate Brokers"}}, {"metadataProperties": {"ObjectId": "eb2cbe96-6057-4786-9d94-3de9862b785a", "RefId": "h2222222-2222-2222-2222-************", "ParentObjectValueId": "c2222222-2222-2222-2222-************", "CustomerName": "<PERSON>", "Address": "Downtown Dubai, UAE", "PhoneNumber": "+971-56-789-0123", "KYCDetails": "Passport: C9876543, Visa: Investor Visa", "Email": "<EMAIL>", "ChannelPartner": "Marina Properties Consultants"}}], "customerPayloads": [{"metadataProperties": {"ObjectId": "6e78fafd-bd5f-4176-8d17-b15b04b2ea95", "RefId": "u1111111-1111-1111-1111-************", "ParentObjectValueId": "a1111111-1111-1111-1111-************", "CustomerType": "Individual", "Email": "<EMAIL>", "KYCDetails": "Emirates ID: 784-1990-7654321-2, Passport: ********", "PhoneNumber": "+971-50-111-2233", "CustomerName": "<PERSON>", "Address": "Al Barsha, Dubai, UAE"}}, {"metadataProperties": {"ObjectId": "6e78fafd-bd5f-4176-8d17-b15b04b2ea95", "RefId": "u2222222-2222-2222-2222-************", "ParentObjectValueId": "a2222222-2222-2222-2222-************", "CustomerType": "Corporate", "Email": "<EMAIL>", "KYCDetails": "Trade License: *********, Tax Registration: TRN-*********", "PhoneNumber": "+971-4-567-8901", "CustomerName": "Global Holdings Investment LLC", "Address": "DIFC, Dubai, UAE"}}], "districtPayloads": [{"metadataProperties": {"ObjectId": "41fa40d3-4c6f-47d0-8759-c056729d8a32", "RefId": "z1111111-1111-1111-1111-************", "ParentObjectValueId": "a1111111-1111-1111-1111-************", "Images": "https://dubai.com/districts/marina/images.jpg", "City": "Dubai", "Description": "Dubai Marina is a prestigious waterfront development featuring luxury residential towers, world-class dining, and stunning marina views.", "CenterCoordinates": "25.0657, 55.1713", "PolygonCoordinates": "25.0600,55.1650 25.0700,55.1650 25.0700,55.1780 25.0600,55.1780", "Country": "United Arab Emirates", "DistrictName": "Dubai Marina"}}, {"metadataProperties": {"ObjectId": "41fa40d3-4c6f-47d0-8759-c056729d8a32", "RefId": "z2222222-2222-2222-2222-************", "ParentObjectValueId": "a2222222-2222-2222-2222-************", "Images": "https://abudhabi.com/districts/corniche/images.jpg", "City": "Abu Dhabi", "Description": "Abu Dhabi Corniche is an iconic waterfront area offering luxury living with panoramic views of the Arabian Gulf and city skyline.", "CenterCoordinates": "24.4764, 54.3705", "PolygonCoordinates": "24.4700,54.3600 24.4800,54.3600 24.4800,54.3800 24.4700,54.3800", "Country": "United Arab Emirates", "DistrictName": "Abu Dhabi Corniche"}}]}