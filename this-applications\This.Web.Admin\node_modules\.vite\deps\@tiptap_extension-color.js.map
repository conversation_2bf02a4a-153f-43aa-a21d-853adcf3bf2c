{"version": 3, "sources": ["../../@tiptap/extension-color/src/color.ts"], "sourcesContent": ["import '@tiptap/extension-text-style'\n\nimport { Extension } from '@tiptap/core'\n\nexport type ColorOptions = {\n  /**\n   * The types where the color can be applied\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n  */\n  types: string[],\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    color: {\n      /**\n       * Set the text color\n       * @param color The color to set\n       * @example editor.commands.setColor('red')\n       */\n      setColor: (color: string) => ReturnType,\n\n      /**\n       * Unset the text color\n       * @example editor.commands.unsetColor()\n       */\n      unsetColor: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/color\n */\nexport const Color = Extension.create<ColorOptions>({\n  name: 'color',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          color: {\n            default: null,\n            parseHTML: element => element.style.color?.replace(/['\"]+/g, ''),\n            renderHTML: attributes => {\n              if (!attributes.color) {\n                return {}\n              }\n\n              return {\n                style: `color: ${attributes.color}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setColor: color => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { color })\n          .run()\n      },\n      unsetColor: () => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { color: null })\n          .removeEmptyTextStyle()\n          .run()\n      },\n    }\n  },\n})\n"], "mappings": ";;;;;;;AAoCa,IAAA,QAAQ,UAAU,OAAqB;EAClD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,OAAO,CAAC,WAAW;;;EAIvB,sBAAmB;AACjB,WAAO;MACL;QACE,OAAO,KAAK,QAAQ;QACpB,YAAY;UACV,OAAO;YACL,SAAS;YACT,WAAW,aAAO;AAAA,kBAAA;AAAI,sBAAA,KAAA,QAAQ,MAAM,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,UAAU,EAAE;YAAC;YAChE,YAAY,gBAAa;AACvB,kBAAI,CAAC,WAAW,OAAO;AACrB,uBAAO,CAAA;;AAGT,qBAAO;gBACL,OAAO,UAAU,WAAW,KAAK;;;UAGtC;QACF;MACF;;;EAIL,cAAW;AACT,WAAO;MACL,UAAU,WAAS,CAAC,EAAE,MAAK,MAAM;AAC/B,eAAO,MAAK,EACT,QAAQ,aAAa,EAAE,MAAK,CAAE,EAC9B,IAAG;;MAER,YAAY,MAAM,CAAC,EAAE,MAAK,MAAM;AAC9B,eAAO,MAAK,EACT,QAAQ,aAAa,EAAE,OAAO,KAAI,CAAE,EACpC,qBAAoB,EACpB,IAAG;;;;AAIb,CAAA;", "names": []}