{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAMD,IAAI,sCAAgB;AACpB,MAAM,yCAAmB,IAAI;AAEtB,SAAS,0CAAe,WAAoB;IACjD,IAAI,CAAC,IAAI,MAAM,GAAG,CAAA,GAAA,eAAO;IAEzB,CAAA,GAAA,yCAAc,EAAE;QACd,IAAI,CAAC,aACH;QAGF,IAAI,OAAO,uCAAiB,GAAG,CAAC;QAChC,IAAI,CAAC,MAAM;YACT,IAAI,KAAK,CAAC,uBAAuB,EAAE,uCAAiB;YACpD,MAAM;YAEN,IAAI,OAAO,SAAS,aAAa,CAAC;YAClC,KAAK,EAAE,GAAG;YACV,KAAK,KAAK,CAAC,OAAO,GAAG;YACrB,KAAK,WAAW,GAAG;YACnB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;gBAAC,UAAU;gBAAG,SAAS;YAAI;YAClC,uCAAiB,GAAG,CAAC,aAAa;QACpC,OACE,MAAM,KAAK,OAAO,CAAC,EAAE;QAGvB,KAAK,QAAQ;QACb,OAAO;YACL,IAAI,QAAQ,EAAE,KAAK,QAAQ,KAAK,GAAG;gBACjC,KAAK,OAAO,CAAC,MAAM;gBACnB,uCAAiB,MAAM,CAAC;YAC1B;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL,oBAAoB,cAAc,KAAK;IACzC;AACF", "sources": ["packages/@react-aria/utils/src/useDescription.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps} from '@react-types/shared';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useState} from 'react';\n\nlet descriptionId = 0;\nconst descriptionNodes = new Map<string, {refCount: number, element: Element}>();\n\nexport function useDescription(description?: string): AriaLabelingProps {\n  let [id, setId] = useState<string | undefined>();\n\n  useLayoutEffect(() => {\n    if (!description) {\n      return;\n    }\n\n    let desc = descriptionNodes.get(description);\n    if (!desc) {\n      let id = `react-aria-description-${descriptionId++}`;\n      setId(id);\n\n      let node = document.createElement('div');\n      node.id = id;\n      node.style.display = 'none';\n      node.textContent = description;\n      document.body.appendChild(node);\n      desc = {refCount: 0, element: node};\n      descriptionNodes.set(description, desc);\n    } else {\n      setId(desc.element.id);\n    }\n\n    desc.refCount++;\n    return () => {\n      if (desc && --desc.refCount === 0) {\n        desc.element.remove();\n        descriptionNodes.delete(description);\n      }\n    };\n  }, [description]);\n\n  return {\n    'aria-describedby': description ? id : undefined\n  };\n}\n"], "names": [], "version": 3, "file": "useDescription.module.js.map"}