import{useState as i}from"react";import{useIsoMorphicEffect as s}from'./use-iso-morphic-effect.js';function f(){var t;let[e]=i(()=>typeof window!="undefined"&&typeof window.matchMedia=="function"?window.matchMedia("(pointer: coarse)"):null),[o,c]=i((t=e==null?void 0:e.matches)!=null?t:!1);return s(()=>{if(!e)return;function n(r){c(r.matches)}return e.addEventListener("change",n),()=>e.removeEventListener("change",n)},[e]),o}export{f as useIsTouchDevice};
